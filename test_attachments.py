#!/usr/bin/env python3
"""
Test script to check attachment functionality in questions, options, master questions, and master options.
"""

import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append('/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from questions.models import Question, Option, MasterQuestion, MasterOption
from questions.serializers import QuestionSerializer, OptionSerializer, MasterQuestionSerializer, MasterOptionSerializer

def test_attachment_fields():
    """Test that attachment fields are properly configured in models and serializers."""
    
    print("🔍 Testing attachment field configurations...")
    
    # Test Question model
    question_fields = [field.name for field in Question._meta.get_fields()]
    print(f"\n📋 Question model fields: {question_fields}")
    
    question_attachment_fields = [
        'attachments', 'explanation_attachment', 'reason_document'
    ]
    
    for field in question_attachment_fields:
        if field in question_fields:
            print(f"✅ Question.{field} - Found")
        else:
            print(f"❌ Question.{field} - Missing")
    
    # Test Option model
    option_fields = [field.name for field in Option._meta.get_fields()]
    print(f"\n📋 Option model fields: {option_fields}")
    
    if 'attachments' in option_fields:
        print("✅ Option.attachments - Found")
    else:
        print("❌ Option.attachments - Missing")
    
    # Test MasterQuestion model
    master_question_fields = [field.name for field in MasterQuestion._meta.get_fields()]
    print(f"\n📋 MasterQuestion model fields: {master_question_fields}")
    
    master_question_attachment_fields = ['attachments', 'reason_document']
    
    for field in master_question_attachment_fields:
        if field in master_question_fields:
            print(f"✅ MasterQuestion.{field} - Found")
        else:
            print(f"❌ MasterQuestion.{field} - Missing")
    
    # Test MasterOption model
    master_option_fields = [field.name for field in MasterOption._meta.get_fields()]
    print(f"\n📋 MasterOption model fields: {master_option_fields}")
    
    master_option_attachment_fields = ['attachments', 'reason_document']
    
    for field in master_option_attachment_fields:
        if field in master_option_fields:
            print(f"✅ MasterOption.{field} - Found")
        else:
            print(f"❌ MasterOption.{field} - Missing")

def test_serializer_fields():
    """Test that serializers include attachment fields."""
    
    print("\n🔍 Testing serializer field configurations...")
    
    # Test QuestionSerializer
    question_serializer = QuestionSerializer()
    question_serializer_fields = list(question_serializer.fields.keys())
    print(f"\n📋 QuestionSerializer fields: {question_serializer_fields}")
    
    question_attachment_fields = [
        'attachments', 'explanation_attachment', 'reason_document', 'options'
    ]
    
    for field in question_attachment_fields:
        if field in question_serializer_fields:
            print(f"✅ QuestionSerializer.{field} - Found")
        else:
            print(f"❌ QuestionSerializer.{field} - Missing")
    
    # Test OptionSerializer
    option_serializer = OptionSerializer()
    option_serializer_fields = list(option_serializer.fields.keys())
    print(f"\n📋 OptionSerializer fields: {option_serializer_fields}")
    
    if 'attachments' in option_serializer_fields:
        print("✅ OptionSerializer.attachments - Found")
    else:
        print("❌ OptionSerializer.attachments - Missing")
    
    # Test MasterQuestionSerializer
    master_question_serializer = MasterQuestionSerializer()
    master_question_serializer_fields = list(master_question_serializer.fields.keys())
    print(f"\n📋 MasterQuestionSerializer fields: {master_question_serializer_fields}")
    
    master_question_attachment_fields = ['attachments', 'reason_document']
    
    for field in master_question_attachment_fields:
        if field in master_question_serializer_fields:
            print(f"✅ MasterQuestionSerializer.{field} - Found")
        else:
            print(f"❌ MasterQuestionSerializer.{field} - Missing")
    
    # Test MasterOptionSerializer
    master_option_serializer = MasterOptionSerializer()
    master_option_serializer_fields = list(master_option_serializer.fields.keys())
    print(f"\n📋 MasterOptionSerializer fields: {master_option_serializer_fields}")
    
    master_option_attachment_fields = ['attachments', 'reason_document']
    
    for field in master_option_attachment_fields:
        if field in master_option_serializer_fields:
            print(f"✅ MasterOptionSerializer.{field} - Found")
        else:
            print(f"❌ MasterOptionSerializer.{field} - Missing")

def test_sample_data():
    """Test with sample data to see if attachments are working."""
    
    print("\n🔍 Testing with sample data...")
    
    # Get some sample questions
    questions = Question.objects.all()[:5]
    print(f"\n📊 Found {questions.count()} sample questions")
    
    for question in questions:
        print(f"\n📝 Question ID: {question.question_id}")
        print(f"   Content: {question.content[:50]}...")
        print(f"   Attachments: {question.attachments}")
        print(f"   Explanation Attachment: {question.explanation_attachment}")
        print(f"   Reason Document: {question.reason_document}")
        
        # Check options
        options = question.options.all()
        print(f"   Options count: {options.count()}")
        for option in options:
            print(f"     Option: {option.option_text[:30]}... | Attachment: {option.attachments}")
    
    # Get some sample master questions
    master_questions = MasterQuestion.objects.all()[:3]
    print(f"\n📊 Found {master_questions.count()} sample master questions")
    
    for master_question in master_questions:
        print(f"\n📝 Master Question ID: {master_question.master_question_id}")
        print(f"   Title: {master_question.title}")
        print(f"   Attachments: {master_question.attachments}")
        print(f"   Reason Document: {master_question.reason_document}")
    
    # Get some sample master options
    master_options = MasterOption.objects.all()[:3]
    print(f"\n📊 Found {master_options.count()} sample master options")
    
    for master_option in master_options:
        print(f"\n📝 Master Option ID: {master_option.master_option_id}")
        print(f"   Title: {master_option.title}")
        print(f"   Attachments: {master_option.attachments}")
        print(f"   Reason Document: {master_option.reason_document}")

if __name__ == "__main__":
    print("🚀 Starting attachment functionality test...")
    
    try:
        test_attachment_fields()
        test_serializer_fields()
        test_sample_data()
        
        print("\n✅ Attachment functionality test completed!")
        
    except Exception as e:
        print(f"\n❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
