from rest_framework import serializers
from django.contrib.auth.models import User
from .models import ContributorProfile
from django.contrib.auth import authenticate
from rest_framework import serializers
from .models import User, Banner
from shashtrarth.utils import validate_username, validate_password


# class UserSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = User
#         fields = [
#             "id",
#             "username",
#             "email",
#             "first_name",
#             "last_name",
#             "password",
#         ]
#         extra_kwargs = {"password": {"write_only": True}}


class ContributorProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContributorProfile
        fields = ["security_question", "security_answer"]


class ContributorRegistrationSerializer(serializers.ModelSerializer):
    contributor_profile = ContributorProfileSerializer()

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "email",
            "password",
            "first_name",
            "last_name",
            "contributor_profile",
        ]
        extra_kwargs = {"password": {"write_only": True}}

    def validate_username(self, value):
        """Validate username using centralized validation utility"""
        return validate_username(value)

    def validate_password(self, value):
        """Validate password using centralized validation utility"""
        return validate_password(value)

    def create(self, validated_data):
        profile_data = validated_data.pop("contributor_profile")
        user = User.objects.create_user(**validated_data)
        ContributorProfile.objects.create(user=user, **profile_data)
        return user

    def update(self, instance, validated_data):
        profile_data = validated_data.pop("contributor_profile", None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        if profile_data:
            profile = instance.contributorprofile
            for attr, value in profile_data.items():
                setattr(profile, attr, value)
            profile.save()

        return instance


class LoginSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)
    password = serializers.CharField(required=True, write_only=True)

    def validate(self, data):
        username = data.get("username")
        password = data.get("password")

        if username and password:
            user = authenticate(username=username, password=password)
            if user:
                if not user.is_active:
                    raise serializers.ValidationError("User account is disabled.")

                # Check if user has a contributor profile
                try:
                    contributor_profile = user.contributor_profile
                except ContributorProfile.DoesNotExist:
                    raise serializers.ValidationError(
                        "Access denied. This account is not authorized for contributor access."
                    )

                # Verify the role is correct
                if contributor_profile.role != "contributor":
                    raise serializers.ValidationError(
                        "Access denied. Invalid role for contributor access."
                    )

                return {"user": user, "role": contributor_profile.role}
            else:
                raise serializers.ValidationError(
                    "Unable to log in with provided credentials."
                )
        else:
            raise serializers.ValidationError("Must include 'username' and 'password'.")

class BannerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Banner
        fields = '__all__'