from django.urls import path
from .views import (
    ContributorDashboardAPIView,
    RegisterView,
    LoginView,
    LogoutView,
    TokenRefreshView,
    BannerListCreateView,
    BannerRetrieveUpdateDestroyView,
    PageVisitors,
    get_all_model_counts
)


urlpatterns = [
    path("register/", RegisterView.as_view(), name="register"),
    path("register/<slug:slug>/", RegisterView.as_view(), name="register-detail"),
    path("login/", LoginView.as_view(), name="login"),
    path("logout/", LogoutView.as_view(), name="logout"),
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path(
        "dashboard/",
        ContributorDashboardAPIView.as_view(),
        name="contributor-dashboard",
    ),
    path('banners/', BannerListCreateView.as_view(), name='banner-list-create'),
    path('banners/<int:pk>/', BannerRetrieveUpdateDestroyView.as_view(), name='banner-retrieve-update-destroy'),
    path("track-page-view/", PageVisitors, name="track_page_view"),
    path('get-all-model-counts/', get_all_model_counts, name='get-all-model-counts'),
]
