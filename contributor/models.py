from django.contrib.auth.models import User
from django.db import models
from django.utils.text import slugify
from questions.utils import generate_unique_slug

class ContributorProfile(models.Model):
    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="contributor_profile"
    )
    role = models.CharField(max_length=30, default="contributor")
    security_question = models.CharField(max_length=255, blank=True, null=True)
    security_answer = models.CharField(max_length=255, blank=True, null=True)
    slug = models.SlugField(max_length=255, unique=True, blank=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(f"{self.user.username}-{self.role}")
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.user.username} - {self.role}"


class ContributorPoints(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=255)
    slug = models.SlugField(max_length=255, unique=True, blank=True)

    normal_questions = models.PositiveIntegerField(
        default=0, help_text="Points for normal questions"
    )
    master_questions = models.PositiveIntegerField(
        default=0, help_text="Points for master questions"
    )
    master_options = models.PositiveIntegerField(
        default=0, help_text="Points for creating master options"
    )
    blogs = models.PositiveIntegerField(default=0, help_text="Points for writing blogs")
    previous_questions = models.PositiveIntegerField(
        default=0, help_text="Points for answering previous questions"
    )

    last_updated = models.DateTimeField(
        auto_now=True, help_text="Last updated timestamp"
    )
    created_at = models.DateTimeField(auto_now_add=True, help_text="Created timestamp")

    class Meta:
        verbose_name = "Contributor Point"
        verbose_name_plural = "Contributor Points"
        ordering = ["-last_updated"]

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name}"


class Banner(models.Model):
    banner_image = models.ImageField(upload_to="banner/")
    banner_name = models.CharField(max_length=255) 
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(Banner,self.banner_name)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.banner_name}" 

class PageCounter(models.Model):
    url = models.CharField(max_length=255) 
    count = models.PositiveIntegerField(default=0)  
    created_at = models.DateTimeField(auto_now_add=True)
    def __str__(self):
        return f"{self.url}: {self.count}"