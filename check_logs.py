#!/usr/bin/env python3
"""
Quick script to check log status
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

from log_admin.models import PerformanceLog, ErrorLog, LogConfig
from django.utils import timezone
from datetime import timedelta

def main():
    print("🔍 Checking Log Status")
    print("="*50)
    
    # Get active config
    config = LogConfig.get_active_config()
    print(f"Active Config: {config.name}")
    print(f"General retention: {config.log_retention_days} days")
    print(f"Error retention: {config.error_retention_days} days")
    print(f"Security retention: {config.security_retention_days} days")
    print()
    
    # Check performance logs
    now = timezone.now()
    general_cutoff = now - timedelta(days=config.log_retention_days)
    
    total_perf_logs = PerformanceLog.objects.count()
    old_perf_logs = PerformanceLog.objects.filter(created_at__lt=general_cutoff).count()
    
    print(f"Performance Logs:")
    print(f"  Total: {total_perf_logs}")
    print(f"  Old (>{config.log_retention_days} days): {old_perf_logs}")
    
    if PerformanceLog.objects.exists():
        oldest = PerformanceLog.objects.order_by('created_at').first()
        newest = PerformanceLog.objects.order_by('-created_at').first()
        oldest_age = (now - oldest.created_at).days
        print(f"  Oldest: {oldest.created_at} ({oldest_age} days old)")
        print(f"  Newest: {newest.created_at}")
    print()
    
    # Check error logs
    error_cutoff = now - timedelta(days=config.error_retention_days)
    total_error_logs = ErrorLog.objects.count()
    old_error_logs = ErrorLog.objects.filter(timestamp__lt=error_cutoff).count()
    
    print(f"Error Logs:")
    print(f"  Total: {total_error_logs}")
    print(f"  Old (>{config.error_retention_days} days): {old_error_logs}")
    
    if ErrorLog.objects.exists():
        oldest = ErrorLog.objects.order_by('timestamp').first()
        newest = ErrorLog.objects.order_by('-timestamp').first()
        oldest_age = (now - oldest.timestamp).days
        print(f"  Oldest: {oldest.timestamp} ({oldest_age} days old)")
        print(f"  Newest: {newest.timestamp}")
    print()
    
    # Summary
    total_old_logs = old_perf_logs + old_error_logs
    print(f"Total logs eligible for cleanup: {total_old_logs}")

if __name__ == '__main__':
    main()
