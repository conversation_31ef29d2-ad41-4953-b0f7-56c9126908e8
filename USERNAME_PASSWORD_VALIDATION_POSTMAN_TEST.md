# Username and Password Validation Testing Guide

## Overview
This document provides comprehensive Postman tests to verify that all signup processes (students, contributors, customer care) properly validate username and password according to the new criteria.

## Validation Criteria

### Username Requirements:
- **Minimum 6 characters**
- **Alphanumeric only** (letters and numbers, no special characters)

### Password Requirements:
- **Must contain alphabets**
- **Must be combined with numbers OR special characters (#, -, _)**

---

## 🧪 Test Cases

### 1. Student Registration Tests

#### **Test 1.1: Valid Student Registration**
**Method:** `POST`  
**URL:** `http://localhost:8000/api/students/register/`  
**Headers:**
```json
{
    "Content-Type": "application/json"
}
```
**Body:**
```json
{
    "user": {
        "username": "student123",
        "email": "<EMAIL>",
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON>",
        "password": "mypass123"
    },
    "phone": "9876543210",
    "address": "123 Main St",
    "course": "Engineering",
    "state": "Maharashtra",
    "gender": "Male",
    "language_preferred": "English"
}
```
**Expected Result:** ✅ Success (200/201) - Registration should proceed to OTP verification

#### **Test 1.2: Invalid Username - Too Short**
**Method:** `POST`  
**URL:** `http://localhost:8000/api/students/register/`  
**Body:**
```json
{
    "user": {
        "username": "abc",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "password": "mypass123"
    },
    "phone": "9876543210",
    "address": "123 Main St",
    "course": "Engineering",
    "state": "Maharashtra",
    "gender": "Male",
    "language_preferred": "English"
}
```
**Expected Result:** ❌ Error (400) - "Username must be at least 6 characters long."

#### **Test 1.3: Invalid Username - Special Characters**
**Method:** `POST`  
**URL:** `http://localhost:8000/api/students/register/`  
**Body:**
```json
{
    "user": {
        "username": "user@123",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "password": "mypass123"
    },
    "phone": "9876543210",
    "address": "123 Main St",
    "course": "Engineering",
    "state": "Maharashtra",
    "gender": "Male",
    "language_preferred": "English"
}
```
**Expected Result:** ❌ Error (400) - "Username must contain only letters and numbers (alphanumeric characters)."

#### **Test 1.4: Invalid Password - Only Numbers**
**Method:** `POST`  
**URL:** `http://localhost:8000/api/students/register/`  
**Body:**
```json
{
    "user": {
        "username": "student123",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "password": "123456"
    },
    "phone": "9876543210",
    "address": "123 Main St",
    "course": "Engineering",
    "state": "Maharashtra",
    "gender": "Male",
    "language_preferred": "English"
}
```
**Expected Result:** ❌ Error (400) - "Password must contain at least one letter."

#### **Test 1.5: Invalid Password - Only Letters**
**Method:** `POST`  
**URL:** `http://localhost:8000/api/students/register/`  
**Body:**
```json
{
    "user": {
        "username": "student123",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "password": "abcdef"
    },
    "phone": "9876543210",
    "address": "123 Main St",
    "course": "Engineering",
    "state": "Maharashtra",
    "gender": "Male",
    "language_preferred": "English"
}
```
**Expected Result:** ❌ Error (400) - "Password must contain letters combined with numbers or special characters (#, -, _)."

#### **Test 1.6: Valid Password with Special Characters**
**Method:** `POST`  
**URL:** `http://localhost:8000/api/students/register/`  
**Body:**
```json
{
    "user": {
        "username": "student123",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "password": "mypass#"
    },
    "phone": "9876543210",
    "address": "123 Main St",
    "course": "Engineering",
    "state": "Maharashtra",
    "gender": "Male",
    "language_preferred": "English"
}
```
**Expected Result:** ✅ Success (200/201) - Registration should proceed to OTP verification

---

### 2. Contributor Registration Tests

#### **Test 2.1: Valid Contributor Registration**
**Method:** `POST`  
**URL:** `http://localhost:8000/api/contributor/register/`  
**Headers:**
```json
{
    "Content-Type": "application/json"
}
```
**Body:**
```json
{
    "username": "contrib123",
    "email": "<EMAIL>",
    "first_name": "Jane",
    "last_name": "Smith",
    "password": "contrib123",
    "contributor_profile": {
        "security_question": "What is your pet's name?",
        "security_answer": "Fluffy"
    }
}
```
**Expected Result:** ✅ Success (201) - Contributor created successfully

#### **Test 2.2: Invalid Contributor Username**
**Method:** `POST`  
**URL:** `http://localhost:8000/api/contributor/register/`  
**Body:**
```json
{
    "username": "abc",
    "email": "<EMAIL>",
    "first_name": "Jane",
    "last_name": "Smith",
    "password": "contrib123",
    "contributor_profile": {
        "security_question": "What is your pet's name?",
        "security_answer": "Fluffy"
    }
}
```
**Expected Result:** ❌ Error (400) - "Username must be at least 6 characters long."

#### **Test 2.3: Invalid Contributor Password**
**Method:** `POST`  
**URL:** `http://localhost:8000/api/contributor/register/`  
**Body:**
```json
{
    "username": "contrib123",
    "email": "<EMAIL>",
    "first_name": "Jane",
    "last_name": "Smith",
    "password": "123456",
    "contributor_profile": {
        "security_question": "What is your pet's name?",
        "security_answer": "Fluffy"
    }
}
```
**Expected Result:** ❌ Error (400) - "Password must contain at least one letter."

---

### 3. Customer Care Registration Tests

#### **Test 3.1: Valid Customer Care Registration**
**Method:** `POST`  
**URL:** `http://localhost:8000/api/customrcare/profiles/`  
**Headers:**
```json
{
    "Content-Type": "application/json",
    "Authorization": "Bearer YOUR_ADMIN_TOKEN"
}
```
**Body:**
```json
{
    "user": {
        "username": "care123",
        "email": "<EMAIL>",
        "first_name": "Support",
        "last_name": "Agent",
        "password": "care123"
    },
    "contact": 9876543210,
    "role": "customrcare"
}
```
**Expected Result:** ✅ Success (201) - Profile created successfully

#### **Test 3.2: Invalid Customer Care Username**
**Method:** `POST`  
**URL:** `http://localhost:8000/api/customrcare/profiles/`  
**Body:**
```json
{
    "user": {
        "username": "abc",
        "email": "<EMAIL>",
        "first_name": "Support",
        "last_name": "Agent",
        "password": "care123"
    },
    "contact": 9876543210,
    "role": "customrcare"
}
```
**Expected Result:** ❌ Error (400) - "Username must be at least 6 characters long."

#### **Test 3.3: Invalid Customer Care Password**
**Method:** `POST`  
**URL:** `http://localhost:8000/api/customrcare/profiles/`  
**Body:**
```json
{
    "user": {
        "username": "care123",
        "email": "<EMAIL>",
        "first_name": "Support",
        "last_name": "Agent",
        "password": "abcdef"
    },
    "contact": 9876543210,
    "role": "customrcare"
}
```
**Expected Result:** ❌ Error (400) - "Password must contain letters combined with numbers or special characters (#, -, _)."

---

## 🔍 Additional Edge Case Tests

### **Test 4.1: Username with Mixed Case**
**Username:** `User123` (should be valid)

### **Test 4.2: Password with Dash**
**Password:** `mypass-123` (should be valid)

### **Test 4.3: Password with Underscore**
**Password:** `my_pass` (should be valid)

### **Test 4.4: Password with Hash**
**Password:** `pass#word` (should be valid)

### **Test 4.5: Empty Username**
**Username:** `""` (should be invalid)

### **Test 4.6: Empty Password**
**Password:** `""` (should be invalid)

---

## 📊 Expected Behavior Summary

### ✅ **Valid Examples:**
- **Usernames:** `user123`, `student456`, `contrib789`, `User123`
- **Passwords:** `pass123`, `mypass#`, `secure-pass`, `test_123`

### ❌ **Invalid Examples:**
- **Usernames:** `abc` (too short), `user@123` (special chars), `user.name` (special chars)
- **Passwords:** `123456` (no letters), `abcdef` (no numbers/special chars), `pass@word` (invalid special char)

---

## 🚀 Testing Instructions

1. **Import** this collection into Postman
2. **Set up environment** variables for your local server
3. **Run tests sequentially** to verify all validation rules
4. **Check response codes** and error messages match expectations
5. **Verify** that valid registrations proceed correctly

---

## 📝 Notes

- All tests assume the server is running on `localhost:8000`
- Customer care tests may require admin authentication
- OTP verification is a separate step for student registration
- Error messages should be clear and user-friendly
