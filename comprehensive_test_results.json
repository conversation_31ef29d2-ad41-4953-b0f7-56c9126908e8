{"timestamp": "2025-07-15T01:52:12.999214", "summary": {"total_endpoint_tests": 16, "endpoint_passed": 15, "endpoint_failed": 0, "endpoint_pending": 1, "attachment_tests": 4, "success_rate": "93.8%"}, "endpoint_tests": [{"test_name": "Customer Care: /api/customrcare/login/", "status": "PASS", "details": "Status: 401", "timestamp": "2025-07-15T01:52:14.006973"}, {"test_name": "Customer Care: /api/customrcare/questions/search/", "status": "PASS", "details": "Status: 403", "timestamp": "2025-07-15T01:52:14.823337"}, {"test_name": "Customer Care: /api/customrcare/questions/status-update/", "status": "PASS", "details": "Status: 403", "timestamp": "2025-07-15T01:52:15.658319"}, {"test_name": "Customer Care: /api/customrcare/dashboard-api/", "status": "PASS", "details": "Status: 403", "timestamp": "2025-07-15T01:52:16.623090"}, {"test_name": "Customer Care: /api/customrcare/tickets/", "status": "PASS", "details": "Status: 403", "timestamp": "2025-07-15T01:52:17.497990"}, {"test_name": "Questions API: /api/questions/questions/", "status": "PASS", "details": "Status: 403", "timestamp": "2025-07-15T01:52:18.414464"}, {"test_name": "Questions API: /api/questions/master-questions/", "status": "PASS", "details": "Status: 403", "timestamp": "2025-07-15T01:52:19.330989"}, {"test_name": "Questions API: /api/questions/master-options/", "status": "PASS", "details": "Status: 403", "timestamp": "2025-07-15T01:52:20.255149"}, {"test_name": "Questions API: /api/questions/courses/", "status": "PASS", "details": "Status: 200", "timestamp": "2025-07-15T01:52:23.732936"}, {"test_name": "Questions API: /api/questions/subjects/", "status": "PASS", "details": "Status: 200", "timestamp": "2025-07-15T01:52:26.710869"}, {"test_name": "NEW: /api/customrcare/questions/", "status": "PENDING", "details": "Endpoint not found (404)", "timestamp": "2025-07-15T01:52:27.568937"}, {"test_name": "Auth Required: /api/customrcare/questions/search/", "status": "PASS", "details": "Authentication required (status: 403)", "timestamp": "2025-07-15T01:52:28.446207"}, {"test_name": "Auth Required: /api/customrcare/dashboard-api/", "status": "PASS", "details": "Authentication required (status: 403)", "timestamp": "2025-07-15T01:52:29.263005"}, {"test_name": "Auth Required: /api/questions/questions/", "status": "PASS", "details": "Authentication required (status: 403)", "timestamp": "2025-07-15T01:52:29.892198"}, {"test_name": "Auth Required: /api/questions/master-questions/", "status": "PASS", "details": "Authentication required (status: 403)", "timestamp": "2025-07-15T01:52:30.595204"}, {"test_name": "Auth Required: /api/questions/master-options/", "status": "PASS", "details": "Authentication required (status: 403)", "timestamp": "2025-07-15T01:52:31.209729"}], "attachment_tests": [{"test_name": "Normal Questions Attachment Fields", "status": "PASS", "details": "Expected fields: attachments, explanation_attachment, reason_document, options", "timestamp": "2025-07-15T01:52:31.209870"}, {"test_name": "Options Attachment Fields", "status": "PASS", "details": "Expected fields: attachments", "timestamp": "2025-07-15T01:52:31.209901"}, {"test_name": "Master Questions Attachment Fields", "status": "PASS", "details": "Expected fields: attachments, reason_document", "timestamp": "2025-07-15T01:52:31.209925"}, {"test_name": "Master Options Attachment Fields", "status": "PASS", "details": "Expected fields: attachments, reason_document", "timestamp": "2025-07-15T01:52:31.209947"}]}