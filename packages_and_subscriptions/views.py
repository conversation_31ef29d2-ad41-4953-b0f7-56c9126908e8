from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from .models import Package, Subscription, Coupon, GiftCard, Subscription_Invoice, FailedPaymentLog
from .serializers import (PackageSerializer, SubscriptionSerializer, CouponValidateSerializer,
                          GiftCardSerializer, CouponSerializer, CouponGenerateSerializer, GiftCardValidateSerializer )
from django.utils import timezone
import razorpay
from django.conf import settings
from dateutil.relativedelta import relativedelta
from    django.shortcuts import get_object_or_404
from wallet_and_transaction.models import Wallet, Transaction
from customrcare.permissions import IsCustomrcareUser
import uuid
from students.models import Student
from django.core.mail import EmailMessage
from django.template.loader import render_to_string
from django.db import transaction

import hmac
import hashlib
from django.conf import settings
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.decorators import action
from django_ratelimit.decorators import ratelimit
from django.core.cache import cache
from dateutil.relativedelta import relativedelta
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type, RetryError


import threading
from django.core.mail import send_mail
from students.models import Referral, ScratchCard
from notifications.models import FCMDevice
from django.conf import settings
from datetime import timedelta
import logging
import random
import string
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from .serializers import SubscriptionInvoiceSerializer
from .notifications import SubscriptionNotificationService
from .payment_service import PaymentService

logger = logging.getLogger(__name__)

# Initialize Razorpay client with error handling
try:
    if not settings.RAZORPAY_KEY_ID or not settings.RAZORPAY_KEY_SECRET:
        logger.error("Razorpay credentials not configured properly")
        client = None
    else:
        client = razorpay.Client(auth=(settings.RAZORPAY_KEY_ID, settings.RAZORPAY_KEY_SECRET))
        logger.info(f"Razorpay client initialized successfully with key: {settings.RAZORPAY_KEY_ID[:10]}...")
except Exception as e:
    logger.error(f"Failed to initialize Razorpay client: {str(e)}")
    client = None

class PackageListCreateView(APIView):
    def get(self, request):
        packages = Package.objects.all()[::-1]
        serializer = PackageSerializer(packages, many=True)
        return Response(serializer.data)

    def post(self, request):
        serializer = PackageSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SubscriptionListCreateView(APIView):
    def get(self, request):
        subscriptions = Subscription.objects.all().order_by("-start_date")
        serializer = SubscriptionSerializer(subscriptions, many=True)
        return Response(serializer.data)
    
    @transaction.atomic
    def post(self, request):
        std_id = request.data.get("student")
        student = get_object_or_404(Student, id=std_id)
        package_id = request.data.get("package")
        coupon_code = request.data.get("coupon")
        gift_card_code = request.data.get("gift_card_code")
        gift_card_pin = request.data.get("gift_card_pin")

        logger.info(f"Creating subscription for student {student.id} with package {package_id}")

        try:
            package = Package.objects.get(id=package_id, is_active=True)
        except Package.DoesNotExist:
            logger.error(f"Package not found or inactive: {package_id}")
            return Response({"error": "Package not found or not available"}, status=404)

        # Validate package availability for event packages
        if package.package_type == 'event' and not package.is_event_active:
            logger.warning(f"Event package {package_id} is not currently active")
            return Response({"error": "This event is not currently available"}, status=400)

        # Initialize payment service
        payment_service = PaymentService()
        
        # Check if coupon is already used by this student (only if coupon_code is povided)
        if coupon_code and Subscription.objects.filter(student=student, coupon_used=coupon_code).exists():
            logger.warning(f"Coupon already used for student: {student.id} with code: {coupon_code}")
            return Response({"error": "Coupon already used"}, status=400)

        final_price = package.discount_price or package.price
        coupon = None
        gift_card = None

        # Apply coupon
        if coupon_code:
            try:
                coupon = Coupon.objects.get(code=coupon_code)
                if not coupon.is_valid():
                    logger.warning(f"Coupon invalid or expired: {coupon_code}")
                    raise Exception("Coupon is invalid or expired")

                if coupon.discount_type == "percentage":
                    final_price -= final_price * (coupon.discount / 100)
                else:
                    final_price -= coupon.discount

                coupon.usage_limit -= 1
                coupon.save()

            except Exception as e:
                logger.error(f"Coupon application failed: {str(e)}")
                return Response({"error": str(e)}, status=400)

        # Apply gift card
        if gift_card_code:
            try:
                gift_card = GiftCard.objects.get(code=gift_card_code)
                if not gift_card.is_valid_combination(gift_card_code, gift_card_pin, gift_card.unique_id):
                    raise Exception("Invalid gift card or PIN")

                # Use as much as possible from the gift card
                if gift_card.balance >= final_price:
                    gift_card.balance -= final_price
                    amount_used = final_price
                    final_price = 0
                    gift_card.used = True
                else:
                    amount_used = gift_card.balance
                    final_price -= gift_card.balance
                    gift_card.balance = 0
                    gift_card.used = True

                gift_card.redeem_at = timezone.now()
                gift_card.save()

                # Send email asynchronously
                logger.info(f"Gift card used by student {student.id}, amount used: ₹{amount_used}")
                def send_giftcard_email():
                    subject = "Gift Card Used Successfully 🎁"
                    message = (
                        f"Hi {student.user.first_name},\n\n"
                        f"You’ve successfully used a gift card ({gift_card.code}) worth ₹{amount_used}.\n"
                        f"Remaining balance: ₹{gift_card.balance}.\n\n"
                        "Thanks for being with us!"
                    )
                    from_email = settings.DEFAULT_FROM_EMAIL
                    recipient_list = [student.user.email]

                    send_mail(subject, message, from_email, recipient_list)

                email_thread = threading.Thread(target=send_giftcard_email)
                email_thread.start()

            except Exception as e:
                logger.error(f"Gift card redemption failed: {str(e)}")
                return Response({"error": str(e)}, status=400)
        
        referral = Referral.objects.filter(referred=student).last()
        print("Sending FCM notification to referrer...4")
        if referral:
            referrer = referral.referrer

            is_first_subscription = not Subscription.objects.filter(student=student, is_active=True).exists()
            print("Sending FCM notification to referrer...3")
            if is_first_subscription:
                referrer_last_sub = Subscription.objects.filter(student=referrer, is_active=True).last()
                print("Sending FCM notification to referrer...2")
                if referrer_last_sub:
                    referrer_price = referrer_last_sub.final_price
                    referee_price = final_price
                    print("Sending FCM notification to referrer...1")
                    if referee_price >= (referrer_price / 2):
                        already_rewarded = ScratchCard.objects.filter(referral=referral).exists()

                        if not already_rewarded:
                            from students.serializers import referral_point_generator
                            reward_amount = referral_point_generator()
                            print("Sending FCM notification to referrer...sueccess")

                            ScratchCard.objects.create(
                                referrer=referrer,
                                referral=referral,
                                amount=reward_amount
                            )
    
                             # Send FCM notification to the referrer
                            print("Sending FCM notification to referrer...")
                            try:
                                from firebase_admin import messaging
                                fcm_devices = FCMDevice.objects.filter(user=referrer.user, is_active=True)
                                for device in fcm_devices:
                                    message = messaging.Message(
                                        notification=messaging.Notification(
                                            title="🎁 You got a Scratch Card!",
                                            body=f"Your friend {student.user.first_name} subscribed! Open your scratch card now.",
                                        ),
                                        token=device.registration_token,
                                        data={
                                            "type": "scratch_card_granted",
                                            "amount": str(reward_amount),
                                            "referee_name": student.user.first_name,
                                        },
                                    )
                                    messaging.send(message)
                            except Exception as e:
                                print("FCM notification error:", e)

                            def send_scratch_card_email():
                                subject = "🎁 You got a Scratch Card!"
                                message = (
                                    f"Hi {student.user.first_name},\n\n"
                                    f"Your friend {student.user.first_name} subscribed! Open your scratch card now."
                                    "Thanks for being with us!"
                                )
                                from_email = settings.DEFAULT_FROM_EMAIL
                                recipient_list = [student.user.email]

                                send_mail(subject, message, from_email, recipient_list)

                            email_thread = threading.Thread(target=send_scratch_card_email)
                            email_thread.start()

        razorpay_order = None
        if final_price > 0:
            if not client:
                logger.error("Razorpay client not initialized")
                return Response({"error": "Payment gateway not available. Please try again later."}, status=500)

            for attempt in range(3):
                try:
                    razorpay_order = client.order.create({
                        "amount": int(final_price * 100),  # in paise
                        "currency": "INR",
                        "payment_capture": 1,
                        "notes": {
                            "student_id": student.id,
                            "package_id": package.id,
                            "package_type": package.package_type,
                            "package_name": package.name
                        }
                    })
                    logger.info(f"Razorpay order created successfully: {razorpay_order['id']} for student {student.id}")
                    break  # Success
                except Exception as e:
                    logger.warning(f"Razorpay order creation attempt {attempt + 1} failed: {e}")
                    if attempt == 2:
                        logger.exception("Razorpay order creation failed after 3 retries")
                        return Response({"error": "Payment failed. Please try again later."}, status=500)
                    
        # Create a subscription object (temporary, update on payment verification)
        existing_subscription = Subscription.objects.filter(
            student=student,
            package=package,
        ).last()

        # Calculate subscription duration based on package type
        if package.package_type == 'validity' and package.duration_months:
            additional_days = package.duration_months * 30
        elif package.package_type == 'event' and package.event_end_date:
            # For event packages, duration is until event end date
            additional_days = (package.event_end_date.date() - timezone.now().date()).days
        else:
            # Default fallback
            additional_days = 30
            logger.warning(f"Using default duration for package {package.id} of type {package.package_type}")

        if existing_subscription:
            # Extend the existing subscription
            existing_subscription.end_date += timedelta(days=additional_days)
            existing_subscription.expiry_date = existing_subscription.end_date
            existing_subscription.final_price += final_price
            if coupon:
                existing_subscription.coupon_used = coupon.code
            if gift_card:
                existing_subscription.gift_card_used = gift_card.code
            existing_subscription.pg_order_id = razorpay_order['id'] if razorpay_order else "GiftCardOnly"
            existing_subscription.transaction_id = str(uuid.uuid4())[:12]
            existing_subscription.save()

            sub = existing_subscription
        else:
            # Create a new subscription
            sub = Subscription.objects.create(
                student=student,
                package=package,
                final_price=final_price,
                pg_order_id=razorpay_order['id'] if razorpay_order else "GiftCardOnly",
                coupon_used=coupon.code if coupon else None,
                gift_card_used=gift_card.code if gift_card else None,
                transaction_id=str(uuid.uuid4())[:12],
            )

        invoice =Subscription_Invoice.objects.create(
            subscription=sub,
            student=student,
            amount=package.discount_price,
            # tax_amount=sub.tax_amount,
            total_amount=sub.final_price,
            is_paid=(final_price == 0),  # If gift card covered it fully
        )

        # Send subscription confirmation notifications
        SubscriptionNotificationService.send_subscription_confirmation(sub)

        def send_invoice_email(invoice):
            subject = f"Invoice #{invoice.invoice_number or invoice.id}"
            context = {
                "invoice": invoice,
                "subscription": invoice.subscription,
                "student": invoice.student,
                "package": invoice.subscription.package,
            }
            body = render_to_string("invoice_email_template.html", context)
            email = EmailMessage(subject, body, to=[invoice.student.user.email])
            email.content_subtype = "html"
            email.send()

        send_invoice_email(invoice)
        logger.info("Razorpay order creation successful")
        
        return Response({
            "subscription_id": sub.id,
            "razorpay_order": razorpay_order if razorpay_order else None,
            "final_price": final_price,
            "currency": "INR"
        })

class SubscriptionDetailView(APIView):
    def get(self, request):
        student_name = request.query_params.get("student_name", None)

        if student_name:
            # Assuming Student model has a ForeignKey or OneToOneField to User
            subscriptions = Subscription.objects.filter(
                student__user__first_name__icontains=student_name, is_active=True
            ) | Subscription.objects.filter(
                student__user__last_name__icontains=student_name, is_active=True
            )

            if subscriptions.exists():
                serializer = SubscriptionSerializer(subscriptions, many=True)
                return Response(serializer.data, status=status.HTTP_200_OK)

            return Response(
                {"error": "No subscriptions found for the given student name"},
                status=status.HTTP_404_NOT_FOUND,
            )
        else:
            return Response(
                {"error": "Please provide a student_name parameter"},
                status=status.HTTP_400_BAD_REQUEST,
            )

class PackageEditDeleteView(APIView):
    def get(self, request, pk=None):
        try:
            package = Package.objects.get(pk=pk)
            serializer = PackageSerializer(package)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Package.DoesNotExist:
            return Response({"error": "Package not found"}, status=status.HTTP_404_NOT_FOUND)

    def put(self, request, pk=None):
        try:
            package = Package.objects.get(pk=pk)
            serializer = PackageSerializer(package, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_200_OK)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Package.DoesNotExist:
            return Response({"error": "Package not found"}, status=status.HTTP_404_NOT_FOUND)

    def delete(self, request, pk=None):
        try:
            package = Package.objects.get(pk=pk)
            package.delete()
            return Response({"message": "Package deleted successfully"}, status=status.HTTP_204_NO_CONTENT)
        except Package.DoesNotExist:
            return Response({"error": "Package not found"}, status=status.HTTP_404_NOT_FOUND) 
        


class VerifySubscriptionPaymentView(APIView):
    @ratelimit(key='ip', rate='5/m', block=True)
    @transaction.atomic
    def post(self, request):
        order_id = request.data.get("razorpay_order_id")
        payment_id = request.data.get("razorpay_payment_id")
        signature = request.data.get("razorpay_signature")
        subscription_id = request.data.get("subscription_id")
        amount = request.data.get("amount")

        if cache.get(f"used_order_id:{order_id}"):
            logger.warning(f"Replay attack detected for order_id: {order_id}")
            return Response({"error": "Payment already processed"}, status=400)

        if not all([order_id, payment_id, signature, subscription_id, amount]):
            logger.error("Missing payment info")
            return Response({"error": "Missing payment info"}, status=400)

        try:
            subscription = Subscription.objects.select_for_update().get(id=subscription_id)
        except Subscription.DoesNotExist:
            logger.error(f"Subscription not found: {subscription_id}")
            return Response({"error": "Subscription not found"}, status=404)
        

        if subscription.is_active:
            logger.info(f"Subscription already active for ID: {subscription_id}")
            return Response({"message": "Subscription already active"}, status=200)
        
        expected_amount = subscription.final_price * 100
        if int(amount) != expected_amount:
            logger.error(f"Amount mismatch. Expected {expected_amount}, got {amount}")
            return Response({"error": "Amount mismatch"}, status=400)

        try:
            # Get student from subscription for proper logging
            student = subscription.student
            self.verify_signature_with_retry(order_id, payment_id, signature, student)
        except razorpay.errors.SignatureVerificationError:
            logger.exception(f"Invalid Razorpay signature for subscription {subscription_id}")
            return Response({"error": "Invalid payment signature"}, status=400)
        except Exception as e:
            logger.exception(f"Signature verification error for subscription {subscription_id}: {str(e)}")
            return Response({"error": "Payment verification failed"}, status=400)

        try:
            self.activate_subscription_with_retry(subscription, payment_id, signature)
            cache.set(f"used_order_id:{order_id}", True, timeout=3600)
            logger.info(f"Subscription activated successfully | ID: {subscription_id}")
            return Response({"message": "Payment verified and subscription activated."}, status=200)
        except Exception as e:
            logger.exception(f"Final failure after retries: {str(e)}")
            transaction.set_rollback(True)
            return Response({"error": "Payment processing failed. Try again later."}, status=500)

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry=retry_if_exception_type(razorpay.errors.SignatureVerificationError)
    )
    def verify_signature_with_retry(self, order_id, payment_id, signature, student):
        try:
            if not client:
                raise Exception("Razorpay client not initialized")

            client.utility.verify_payment_signature({
                'razorpay_order_id': order_id,
                'razorpay_payment_id': payment_id,
                'razorpay_signature': signature
            })
            logger.info(f"Payment signature verified successfully for student {student.id}")

        except RetryError as e:
            error_msg = str(e.last_attempt.exception())
            logger.error(f"Signature verification failed after retries: {error_msg}")
            FailedPaymentLog.objects.create(
                user=student,
                order_id=order_id,
                payment_id=payment_id,
                reason=error_msg
            )
            raise
        except Exception as e:
            logger.error(f"Signature verification error: {str(e)}")
            FailedPaymentLog.objects.create(
                user=student,
                order_id=order_id,
                payment_id=payment_id,
                reason=str(e)
            )
            raise
       

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=5),
        retry=retry_if_exception_type(Exception)
    )
    def activate_subscription_with_retry(self, subscription, payment_id, signature):
        subscription.pg_payment_id = payment_id
        subscription.pg_signature = signature
        subscription.is_active = True
        subscription.start_date = timezone.now().date()

        # Calculate end date based on package type
        if subscription.package.package_type == 'validity' and subscription.package.duration_months:
            subscription.end_date = subscription.start_date + relativedelta(months=subscription.package.duration_months)
        elif subscription.package.package_type == 'event' and subscription.package.event_end_date:
            subscription.end_date = subscription.package.event_end_date.date()
        else:
            # Default fallback
            subscription.end_date = subscription.start_date + relativedelta(months=1)

        # Keep expiry_date for backward compatibility
        subscription.expiry_date = subscription.end_date
        subscription.save()

        logger.info(f"Subscription {subscription.id} activated for student {subscription.student.id}")

        invoice = Subscription_Invoice.objects.select_for_update().get(subscription=subscription)
        invoice.is_paid = True
        invoice.save()

        logger.info(f"Invoice {invoice.id} marked as paid for subscription {subscription.id}")

        # Send activation notifications
        SubscriptionNotificationService.send_subscription_activation(subscription)
        
    # def post(self, request):
    #     order_id = request.data.get("razorpay_order_id")
    #     payment_id = request.data.get("razorpay_payment_id")
    #     signature = request.data.get("razorpay_signature")
    #     subscription_id = request.data.get("subscription_id")
    #     amount = request.data.get("amount")

    #     expected_amount = Subscription.objects.get(pg_order_id=order_id).final_price * 100
    #     if int(amount) != expected_amount:
    #         return Response({"error": "Amount mismatch"}, status=400)
        
    #     if not all([order_id, payment_id, signature, subscription_id]):
    #         return Response({"error": "Missing payment info"}, status=400)

    #     try:
    #         # Verify Razorpay signature
    #         params_dict = {
    #             'razorpay_order_id': order_id,
    #             'razorpay_payment_id': payment_id,
    #             'razorpay_signature': signature
    #         }

    #         client.utility.verify_payment_signature(params_dict)
    #     except razorpay.errors.SignatureVerificationError:
    #         return Response({"error": "Invalid payment signature"}, status=400)

    #     try:
    #         subscription = Subscription.objects.get(id=subscription_id)
    #         subscription.pg_payment_id = payment_id
    #         subscription.pg_signature = signature
    #         subscription.is_active = True
    #         subscription.start_date = timezone.now().date()
    #         subscription.expiry_date = subscription.start_date +  relativedelta(months=subscription.package.duration_months)
    #         subscription.save()

    #         # Update invoice as paid
    #         invoice = Subscription_Invoice.objects.get(subscription=subscription)
    #         invoice.is_paid = True
    #         invoice.save()

    #         return Response({"message": "Payment verified and subscription activated."}, status=200)

    #     except Subscription.DoesNotExist:
    #         return Response({"error": "Subscription not found"}, status=404)
    #     except Exception as e:
    #         return Response({"error": str(e)}, status=500)


class CouponViewSet(viewsets.ViewSet):
    permission_classes = [IsCustomrcareUser]

    """
    A ViewSet to handle all coupon operations: create, list, retrieve, update, delete, validate.
    """

    def list(self, request):
        coupons = Coupon.objects.all().order_by("-created_at")
        serializer = CouponSerializer(coupons, many=True)
        return Response(serializer.data)

    def retrieve(self, request, pk=None):
        try:
            coupon = Coupon.objects.get(pk=pk)
            serializer = CouponSerializer(coupon)
            return Response(serializer.data)
        except Coupon.DoesNotExist:
            return Response({"error": "Coupon not found"}, status=status.HTTP_404_NOT_FOUND)

    def create(self, request):
        serializer = CouponGenerateSerializer(data=request.data)
        if serializer.is_valid():
            discount = serializer.validated_data['discount']
            discount_type = serializer.validated_data['discount_type']
            usage_limit = serializer.validated_data.get('usage_limit', None)
            count = serializer.validated_data['count']
            expiry_date = serializer.validated_data['expiry_date']
            codes = serializer.validated_data.get('codes', [])
            print("$$$$", usage_limit)
            if codes and len(codes) != count:
                return Response({
                    "success": False,
                    "error": "Number of provided codes must match the count."
                }, status=status.HTTP_400_BAD_REQUEST)

            generated_coupons = []
            for i in range(count):
                if codes:
                    code = codes[i].upper()
                    if Coupon.objects.filter(code=code).exists():
                        return Response({
                            "success": False,
                            "error": f"Coupon code '{code}' already exists."
                        }, status=status.HTTP_400_BAD_REQUEST)
                else:
                    while True:
                        code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))
                        if not Coupon.objects.filter(code=code).exists():
                            break

                coupon = Coupon.objects.create(
                    code=code,
                    discount=discount,
                    expiry_date=expiry_date,
                    discount_type=discount_type,
                    usage_limit=usage_limit,
                    is_active=True
                )
                generated_coupons.append(coupon.code)

            return Response({
                "success": True,
                "generated_coupons": generated_coupons
            })

        return Response({"success": False, "errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
    
    def update(self, request, pk=None):
        try:
            coupon = Coupon.objects.get(pk=pk)
        except Coupon.DoesNotExist:
            return Response({"error": "Coupon not found"}, status=status.HTTP_404_NOT_FOUND)

        serializer = CouponSerializer(coupon, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({"success": True, "coupon": serializer.data})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk=None):
        try:
            coupon = Coupon.objects.get(pk=pk)
            coupon.delete()
            return Response({"success": True, "message": "Coupon deleted"})
        except Coupon.DoesNotExist:
            return Response({"error": "Coupon not found"}, status=status.HTTP_404_NOT_FOUND)

  
class ValidateCouponAPI(APIView):
    def post(self, request):
        serializer = CouponValidateSerializer(data=request.data)
        if serializer.is_valid():
            coupon_code = serializer.validated_data["coupon_code"]

            try:
                coupon = Coupon.objects.get(code=coupon_code)
                if coupon.is_valid():
                    return Response({
                        "valid": True,
                        "message": "Coupon applied successfully!",
                        "discount": coupon.discount,
                        "discount_type": coupon.discount_type
                    })
                else:
                    return Response({"valid": False, "message": "Coupon is invalid or expired."})
            except Coupon.DoesNotExist:
                return Response({"valid": False, "message": "Invalid coupon code."})

        return Response({"valid": False, "errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
    
from rest_framework import viewsets
from .models import GiftCard
from .serializers import GiftCardSerializer

class GiftCardViewSet(viewsets.ModelViewSet):
    queryset = GiftCard.objects.all().order_by("-created_at")
    serializer_class = GiftCardSerializer

    @action(detail=False, methods=["post"], url_path="validate")
    def validate_giftcard(self, request):
        serializer = GiftCardValidateSerializer(data=request.data) # this serializer is  made for validating gift card code
        if serializer.is_valid():
            code = serializer.validated_data["gift_code"]
            try:
                gift_card = GiftCard.objects.get(code=code, is_active=True, used=False)

                if gift_card.expires_at and gift_card.expires_at.date() < timezone.now().date():
                    return Response({"valid": False, "message": "Gift card has expired."})

                return Response({
                    "valid": True,
                    "message": "Gift card is valid.",
                    "amount": gift_card.balance
                })
            except GiftCard.DoesNotExist:
                return Response({"valid": False, "message": "Invalid or inactive gift card."})
        return Response({"valid": False, "errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
    
@api_view(['POST'])
def redeem_gift_card(request):
    code = request.data.get("code")
    pin = request.data.get("pin")
    uid = request.data.get("uid")

    try:
        gift_card = GiftCard.objects.get(code=code)
        if not gift_card.is_valid_combination(code, pin, uid):
            return Response({"error": "Invalid card or PIN"}, status=400)

        return Response({
            "message": "Gift card valid",
            "balance": gift_card.balance
        })
    except GiftCard.DoesNotExist:
        return Response({"error": "Gift card not found"}, status=404)


@api_view(['POST'])
def redeem_gift_card(request):
    code = request.data.get("code")
    pin = request.data.get("pin")


    try:
        gift_card = GiftCard.objects.get(code=code)
        if not gift_card.is_valid_combination(code, pin, gift_card.unique_id):
            return Response({"error": "Invalid card or PIN"}, status=400)

        return Response({
            "message": "Gift card valid",
            "balance": gift_card.balance
        })
    except GiftCard.DoesNotExist:
        return Response({"error": "Gift card not found"}, status=404)


class GetRazorpayConfigView(APIView):
    def get(self, request):
        return Response({
            "razorpay_key": settings.RAZORPAY_KEY_ID,
            "razorpay_secret": settings.RAZORPAY_KEY_SECRET,
            "currency": "INR",
            "company_name": "My Subscription Platform",
            "logo_url": "",  # optional
        })



@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_invoices(request):
    student = request.user.student_profile
    invoices = Subscription_Invoice.objects.filter(student=student).order_by('-issue_date')
    serializer = SubscriptionInvoiceSerializer(invoices, many=True)
    return Response(serializer.data)

from django.shortcuts import render, get_object_or_404

def invoice_html_view(request, invoice_number):
    invoice = get_object_or_404(Subscription_Invoice, invoice_number=invoice_number)
    return render(request, "user-invoices.html", {"invoice": invoice})