#!/usr/bin/env python3
"""
Test script to create test user and get <PERSON><PERSON><PERSON> token for API testing
"""

import os
import sys
import django
import json

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from django.contrib.auth.models import User
from students.models import Student
from rest_framework_simplejwt.tokens import RefreshToken
import uuid

def create_test_user():
    """Create a test user for API testing"""
    
    # Generate unique username
    username = f"testuser_{uuid.uuid4().hex[:8]}"
    email = f"{username}@example.com"
    phone = f"98765{uuid.uuid4().hex[:5]}"
    
    try:
        # Create user
        user = User.objects.create_user(
            username=username,
            email=email,
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        
        # Create student profile
        student = Student.objects.create(
            user=user,
            phone=phone
        )
        
        # Generate JWT token
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        
        print("=" * 60)
        print("TEST USER CREATED SUCCESSFULLY")
        print("=" * 60)
        print(f"Username: {username}")
        print(f"Email: {email}")
        print(f"Phone: {phone}")
        print(f"Password: testpass123")
        print(f"User ID: {user.id}")
        print(f"Student ID: {student.id}")
        print("=" * 60)
        print("JWT ACCESS TOKEN:")
        print(access_token)
        print("=" * 60)
        
        # Generate curl commands for testing
        print("\nCURL COMMANDS FOR TESTING:")
        print("=" * 60)
        
        # 1. Bulk contact upload
        print("1. BULK CONTACT UPLOAD:")
        upload_data = {
            "contacts": [
                {"name": "John Doe", "contact": "9876543210"},
                {"name": "Jane Smith", "contact": "9876543211"},
                {"name": "Bob Johnson", "contact": "9876543212"},
                {"name": "Alice Brown", "contact": "9876543213"}
            ]
        }
        
        curl_upload = f"""curl -X POST http://localhost:8000/api/contacts/upload/ \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer {access_token}" \\
  -d '{json.dumps(upload_data)}'"""
        print(curl_upload)
        print()
        
        # 2. Get user contacts
        print("2. GET USER CONTACTS:")
        curl_get_contacts = f"""curl -X GET http://localhost:8000/api/contacts/my-contacts/ \\
  -H "Authorization: Bearer {access_token}" """
        print(curl_get_contacts)
        print()
        
        # 3. Get contact relationships
        print("3. GET CONTACT RELATIONSHIPS:")
        curl_get_relationships = f"""curl -X GET http://localhost:8000/api/contacts/relationships/ \\
  -H "Authorization: Bearer {access_token}" """
        print(curl_get_relationships)
        print()
        
        # 4. Search contacts
        print("4. SEARCH CONTACTS:")
        curl_search = f"""curl -X GET "http://localhost:8000/api/contacts/search/?query=John" \\
  -H "Authorization: Bearer {access_token}" """
        print(curl_search)
        print()
        
        # 5. Get contact stats
        print("5. GET CONTACT STATS:")
        curl_stats = f"""curl -X GET http://localhost:8000/api/contacts/stats/ \\
  -H "Authorization: Bearer {access_token}" """
        print(curl_stats)
        print()
        
        # 6. Get mutual contacts
        print("6. GET MUTUAL CONTACTS:")
        curl_mutual = f"""curl -X GET http://localhost:8000/api/contacts/mutual/ \\
  -H "Authorization: Bearer {access_token}" """
        print(curl_mutual)
        print()
        
        print("=" * 60)
        print("INSTRUCTIONS:")
        print("1. Copy and run the curl commands above in a new terminal")
        print("2. The server is running at http://localhost:8000")
        print("3. Test each endpoint and verify the responses")
        print("=" * 60)
        
        return user, student, access_token
        
    except Exception as e:
        print(f"Error creating test user: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None, None

if __name__ == "__main__":
    create_test_user()
