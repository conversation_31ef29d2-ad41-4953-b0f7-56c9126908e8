from django.contrib import admin
from django.urls import path, include

from questions.sitemaps import CourseSitemap, SubCourseSitemap
from students.sitemaps import StaticViewSitemap
from .views import (
    GetSessionDataView,
    GetSessionIDView,
    PasswordResetRequestAPIView,
    OTPVerificationAPIView,
    ResendOTPAPIView
)
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.staticfiles.urls import staticfiles_urlpatterns
from django.contrib.sitemaps.views import sitemap
from blogs.sitemaps import BlogPostSitemap

sitemaps = {
    "blogs": BlogPostSitemap,
    "courses": CourseSitemap,
    "subcourses": SubCourseSitemap,
    "static": StaticViewSitemap,
}

urlpatterns = [
    path("admin/", admin.site.urls),
    path("api/students/", include("students.urls")),
    path("api/blogs/", include("blogs.urls")),
    path("api/contributor/", include("contributor.urls")),
    path("api/customrcare/", include("customrcare.urls")),
    path("api/questions/", include("questions.urls")),
    path("api/test-patterns/", include("paper_engine.urls")),
    path("api/packages/", include("packages_and_subscriptions.urls")),
    path("api/fcm/", include("notifications.urls")),
    path("api/wallet/", include("wallet_and_transaction.urls")),
    # path('api/membership/', include("membership.urls")),
    path(
        "api/password-reset/",
        PasswordResetRequestAPIView.as_view(),
        name="password_reset_request",
    ),
    path(
        "api/otp-verification/",
        OTPVerificationAPIView.as_view(),
        name="otp_verification",
    ),
    path("api/session-id/", GetSessionIDView.as_view(), name="get_session_id"),
    path(
        "api/session/<str:session_id>/",
        GetSessionDataView.as_view(),
        name="get_session_data",
    ),
    path(
        "sitemap.xml",
        sitemap,
        {"sitemaps": sitemaps},
        name="django.contrib.sitemaps.views.sitemap",
    ),
    path('api/resend-otp/', ResendOTPAPIView.as_view(), name='resend-otp'),
    path('api/chat-ai/', include('chat_ai.urls')),
    path('api/log-admin/', include('log_admin.urls')),
    path('api/contacts/', include('contacts.urls'))
]


if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)


urlpatterns += staticfiles_urlpatterns()
