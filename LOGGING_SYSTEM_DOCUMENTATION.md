# Comprehensive Logging System Documentation

## Overview

This document describes the comprehensive logging system implemented for the Django application. The system provides detailed logging, monitoring, and analytics capabilities across all application components.

## Features

### 1. **Multi-Level Logging**
- **Performance Logging**: Request/response times, memory usage, database queries
- **Error Logging**: Detailed error tracking with categorization and resolution tracking
- **User Activity Logging**: Complete user action tracking with session information
- **API Access Logging**: Comprehensive API usage monitoring
- **Database Query Logging**: SQL query performance monitoring
- **Authentication Logging**: Security event tracking
- **Security Incident Logging**: Threat detection and monitoring
- **System Health Logging**: Resource monitoring and alerting

### 2. **Advanced Admin Interface**
- Rich admin interfaces with filtering, search, and analytics
- Color-coded severity indicators
- Bulk actions for error resolution
- Real-time dashboard views
- Export capabilities

### 3. **REST API Endpoints**
- Complete API access to all log types
- Advanced filtering and search capabilities
- Analytics and dashboard endpoints
- Real-time monitoring capabilities

### 4. **Automated Management**
- Log cleanup and retention policies
- System health monitoring
- Report generation
- Automated alerting for critical issues

## Installation and Setup

### 1. **Dependencies**
The system requires the following packages:
```bash
pip install psutil django-filter
```

### 2. **Settings Configuration**
Add to your `INSTALLED_APPS`:
```python
INSTALLED_APPS = [
    # ... other apps
    'log_admin',
]
```

Add middleware to `MIDDLEWARE`:
```python
MIDDLEWARE = [
    # ... other middleware
    'log_admin.middleware.LoggingMiddleware',
    'log_admin.middleware.ActivityLoggingMiddleware',
    'log_admin.middleware.SecurityMiddleware',
]
```

### 3. **Database Migration**
```bash
python manage.py makemigrations log_admin
python manage.py migrate log_admin
```

### 4. **URL Configuration**
Include in your main `urls.py`:
```python
urlpatterns = [
    # ... other URLs
    path('api/log-admin/', include('log_admin.urls')),
]
```

## Configuration

### Log Configuration Model
The system uses a `LogConfig` model to manage logging settings:

- **Level**: Logging level (DEBUG, INFO, WARNING, ERROR)
- **Feature Toggles**: Enable/disable specific logging types
- **Retention**: Log retention policies
- **Limits**: Maximum log entries

Access via Django Admin or API endpoints.

## Usage

### 1. **Using Decorators**

#### Performance Logging
```python
from log_admin.utils import log_performance

@log_performance(include_db_queries=True, include_memory=True)
def my_view(request):
    # Your view logic
    pass
```

#### User Activity Logging
```python
from log_admin.utils import log_user_action

@log_user_action('API_CALL', 'User retrieved profile data')
def get_profile(request):
    # Your view logic
    pass
```

#### Authentication Logging
```python
from log_admin.utils import log_authentication

@log_authentication('LOGIN_SUCCESS')
def login_view(request):
    # Your login logic
    pass
```

#### Error Logging
```python
from log_admin.utils import log_errors

@log_errors(error_type='VALIDATION', severity='LOW')
def validate_data(request):
    # Your validation logic
    pass
```

### 2. **Manual Logging**

#### Log User Activity
```python
from log_admin.utils import LoggingUtils

LoggingUtils.log_user_activity(
    user=request.user,
    activity_type='PAYMENT',
    action='Payment processed',
    description='User completed payment for subscription',
    request=request,
    metadata={'amount': 100, 'currency': 'INR'}
)
```

#### Log Authentication Events
```python
LoggingUtils.log_authentication_event(
    event_type='LOGIN_FAILED',
    username_attempted='<EMAIL>',
    success=False,
    failure_reason='Invalid password',
    request=request
)
```

#### Log Errors
```python
LoggingUtils.log_error_manual(
    error_type='BUSINESS_LOGIC',
    error_message='Payment processing failed',
    view_name='payment_view',
    severity='HIGH',
    user=request.user,
    request=request
)
```

## API Endpoints

### Base URL: `/api/log-admin/`

#### Log Data Endpoints
- `GET /performance/` - Performance logs
- `GET /errors/` - Error logs  
- `GET /activities/` - User activities
- `GET /api-access/` - API access logs
- `GET /database-queries/` - Database query logs
- `GET /authentication/` - Authentication logs
- `GET /security-incidents/` - Security incidents
- `GET /system-health/` - System health logs

#### Analytics Endpoints
- `GET /analytics/?days=7` - Comprehensive analytics
- `GET /dashboard/` - Real-time dashboard data

#### Management Endpoints
- `GET /config/` - Get logging configuration
- `PUT /config/` - Update logging configuration
- `POST /cleanup/` - Trigger log cleanup
- `POST /search/` - Advanced log search

### Query Parameters
All list endpoints support:
- `page` - Page number
- `page_size` - Results per page
- `search` - Search term
- `ordering` - Sort field
- Various filters specific to each log type

## Management Commands

### 1. **Log Cleanup**
```bash
# Clean up old logs based on retention policy
python manage.py cleanup_logs

# Clean up logs older than 30 days
python manage.py cleanup_logs --days 30

# Dry run to see what would be deleted
python manage.py cleanup_logs --dry-run

# Force cleanup without confirmation
python manage.py cleanup_logs --force
```

### 2. **System Health Monitoring**
```bash
# Run single health check
python manage.py monitor_system_health

# Monitor for 60 minutes with 30-second intervals
python manage.py monitor_system_health --duration 60 --interval 30

# Verbose output
python manage.py monitor_system_health --verbose
```

### 3. **Generate Reports**
```bash
# Generate 7-day report in JSON format
python manage.py generate_log_report

# Generate 30-day report in CSV format
python manage.py generate_log_report --days 30 --format csv

# Generate specific sections only
python manage.py generate_log_report --include errors performance

# Custom output path
python manage.py generate_log_report --output /path/to/report.json
```

## Admin Interface

### Accessing Logs
1. Go to Django Admin
2. Navigate to "Log Admin" section
3. Select the log type you want to view

### Features
- **Advanced Filtering**: Filter by date, user, type, severity, etc.
- **Search**: Full-text search across relevant fields
- **Bulk Actions**: Mark errors as resolved, incidents as blocked
- **Color Coding**: Visual indicators for severity levels
- **Export**: Download filtered results

### Dashboard Views
- Real-time metrics
- Error summaries
- Performance trends
- Security alerts

## Security Features

### Threat Detection
The system automatically detects and logs:
- SQL injection attempts
- XSS attempts
- Path traversal attempts
- Brute force attacks
- Rate limit violations

### Security Incident Management
- Automatic incident creation
- Severity classification
- Resolution tracking
- IP blocking capabilities

## Performance Monitoring

### Metrics Tracked
- Response times
- Memory usage
- CPU usage
- Database query counts and times
- Request throughput
- Error rates

### Alerting
- Configurable thresholds
- Critical alert logging
- System health status tracking

## Best Practices

### 1. **Configuration**
- Set appropriate retention policies
- Enable only needed logging types in production
- Configure proper log levels
- Set reasonable thresholds for alerts

### 2. **Performance**
- Monitor log storage usage
- Run regular cleanup
- Use database indexes effectively
- Consider log archiving for long-term storage

### 3. **Security**
- Regularly review security incidents
- Monitor authentication failures
- Set up alerting for critical security events
- Review and resolve security incidents promptly

### 4. **Maintenance**
- Schedule regular log cleanup
- Monitor system health metrics
- Generate periodic reports
- Review and update logging configuration

## Troubleshooting

### Common Issues

#### High Log Volume
- Adjust retention policies
- Disable unnecessary logging types
- Increase cleanup frequency

#### Performance Impact
- Reduce logging detail level
- Disable database query logging in production
- Optimize database indexes

#### Storage Issues
- Implement log archiving
- Reduce retention periods
- Use log rotation

### Monitoring Log System Health
- Check log configuration regularly
- Monitor log storage usage
- Review system health metrics
- Ensure cleanup commands are running

## Integration Examples

### Custom Views
```python
from log_admin.utils import log_performance, log_user_action

class CustomAPIView(APIView):
    @log_performance(include_db_queries=True)
    @log_user_action('API_CALL', 'Custom API endpoint accessed')
    def get(self, request):
        # Your logic here
        return Response(data)
```

### Error Handling
```python
from log_admin.utils import LoggingUtils

def process_payment(request, amount):
    try:
        # Payment processing logic
        result = payment_gateway.process(amount)
        
        LoggingUtils.log_user_activity(
            user=request.user,
            activity_type='PAYMENT',
            action=f'Payment processed: ${amount}',
            request=request,
            metadata={'amount': amount, 'gateway': 'razorpay'}
        )
        
        return result
    except PaymentError as e:
        LoggingUtils.log_error_manual(
            error_type='EXTERNAL_API',
            error_message=str(e),
            view_name='process_payment',
            severity='HIGH',
            user=request.user,
            request=request,
            additional_data={'amount': amount}
        )
        raise
```

This comprehensive logging system provides complete visibility into your application's behavior, performance, and security posture.
