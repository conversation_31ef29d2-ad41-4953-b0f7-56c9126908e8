import sqlite3

def remove_duplicate_subscriptions(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        cursor.execute("""
            DELETE FROM packages_and_subscriptions_subscription
            WHERE rowid NOT IN (SELECT min(rowid) FROM packages_and_subscriptions_subscription GROUP BY subscription_id);
        """)

        conn.commit()
        print("Duplicate subscription_ids removed.")
    except sqlite3.OperationalError as e:
        print(f"Error: {e}")
        print("Verify table and column names, and that the migration was applied.")

    conn.close()

if __name__ == "__main__":
    db_path = "D:\\working\\shash_b\\db.sqlite3" #replace with your database path.
    remove_duplicate_subscriptions(db_path)